# Variables and basic data types
x = 42                  # Integer
pi = 3.14159           # Float
name = "Python"        # String
is_awesome = True      # Boolean

# Basic operators
sum_result = 10 + 5    # Addition
diff = 10 - 5          # Subtraction
product = 10 * 5       # Multiplication
quotient = 10 / 5      # Division (returns float)
floor_div = 10 // 3    # Floor division
remainder = 10 % 3     # Modulo
power = 2 ** 3         # Exponentiation

# String operations
greeting = "Hello"
target = "World"
message = f"{greeting}, {target}!"  # f-strings
print(message)

# Type conversion
str_num = "123"
num = int(str_num)     # String to integer
float_num = float(num) # Integer to float
str_back = str(num)    # Number to string

# Display types and values
print(f"Type of x: {type(x)}, Value: {x}")
print(f"Type of pi: {type(pi)}, Value: {pi}")
print(f"Type of name: {type(name)}, Value: {name}")
print(f"Type of is_awesome: {type(is_awesome)}, Value: {is_awesome}")

# Conditional statements
score = 85

if score >= 90:
    grade = 'A'
elif score >= 80:
    grade = 'B'
elif score >= 70:
    grade = 'C'
else:
    grade = 'F'

print(f"Score: {score}, Grade: {grade}")

# For loop examples
print("\nFor loop with range:")
for i in range(5):
    print(f"Iteration {i}")

print("\nFor loop with list:")
fruits = ['apple', 'banana', 'cherry']
for fruit in fruits:
    print(fruit)

# While loop with break and continue
print("\nWhile loop with break and continue:")
counter = 0
while True:
    counter += 1
    if counter == 3:
        continue  # Skip iteration when counter is 3
    if counter > 5:
        break    # Exit loop when counter exceeds 5
    print(f"Counter: {counter}")

# Loop with enumerate
print("\nEnumerate example:")
for index, fruit in enumerate(fruits):
    print(f"Index {index}: {fruit}")

# List comprehension (combining loops and conditionals)
numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
even_numbers = [x for x in numbers if x % 2 == 0]
print(f"\nEven numbers using list comprehension: {even_numbers}")

# Basic function definition
def greet(name, greeting="Hello"):
    """
    A simple greeting function with a default parameter.
    
    Args:
        name (str): The name to greet
        greeting (str, optional): The greeting to use. Defaults to "Hello"
    
    Returns:
        str: The complete greeting message
    """
    return f"{greeting}, {name}!"

# Function calls
print(greet("Alice"))
print(greet("Bob", greeting="Hi"))

# Function with multiple return values
def divide_and_remainder(a, b):
    return a // b, a % b

quotient, remainder = divide_and_remainder(10, 3)
print(f"10 divided by 3: quotient = {quotient}, remainder = {remainder}")

# Args and kwargs
def example_function(*args, **kwargs):
    print("Positional arguments:", args)
    print("Keyword arguments:", kwargs)

example_function(1, 2, 3, name="Alice", age=30)

# Lambda functions
square = lambda x: x**2
cube = lambda x: x**3

numbers = [1, 2, 3, 4, 5]
squares = list(map(square, numbers))
cubes = list(map(cube, numbers))

print(f"Numbers: {numbers}")
print(f"Squares: {squares}")
print(f"Cubes: {cubes}")

# Sorting with lambda
pairs = [(1, 'one'), (2, 'two'), (3, 'three')]
# Sort by the second element (string)
sorted_by_string = sorted(pairs, key=lambda pair: pair[1])
print(f"\nSorted by string: {sorted_by_string}")

# Higher-order function example
def create_multiplier(factor):
    return lambda x: x * factor

double = create_multiplier(2)
triple = create_multiplier(3)

print(f"\nDouble 5: {double(5)}")
print(f"Triple 5: {triple(5)}")

# Lists
print("=== Lists ===")
fruits = ['apple', 'banana', 'cherry']
print(f"Original list: {fruits}")

# List operations
fruits.append('date')
fruits.insert(1, 'blueberry')
print(f"After append and insert: {fruits}")

fruits.remove('banana')
popped_fruit = fruits.pop()
print(f"After remove and pop: {fruits}")

# List slicing
numbers = [0, 1, 2, 3, 4, 5]
print(f"\nOriginal numbers: {numbers}")
print(f"First three: {numbers[:3]}")
print(f"Last three: {numbers[-3:]}")
print(f"Every second number: {numbers[::2]}")

# Tuples
print("\n=== Tuples ===")
# Tuples are immutable
coordinates = (3, 4)
x, y = coordinates  # Tuple unpacking
print(f"Coordinates: {coordinates}")
print(f"x: {x}, y: {y}")

# Named tuples
from collections import namedtuple
Point = namedtuple('Point', ['x', 'y'])
p = Point(5, 10)
print(f"Named tuple point: {p}")
print(f"Access by name: p.x = {p.x}, p.y = {p.y}")

# Sets
print("\n=== Sets ===")
fruits_set = {'apple', 'banana', 'cherry', 'apple'}  # Note: duplicates are removed
print(f"Fruits set: {fruits_set}")

# Set operations
more_fruits = {'cherry', 'date', 'elderberry'}
print(f"More fruits: {more_fruits}")
print(f"Union: {fruits_set | more_fruits}")
print(f"Intersection: {fruits_set & more_fruits}")
print(f"Difference: {fruits_set - more_fruits}")

# Dictionaries
print("\n=== Dictionaries ===")
person = {
    'name': 'Alice',
    'age': 30,
    'city': 'New York'
}

# Dictionary operations
print(f"Original dictionary: {person}")
person['email'] = '<EMAIL>'
print(f"After adding email: {person}")

# Dictionary methods
print(f"Keys: {list(person.keys())}")
print(f"Values: {list(person.values())}")
print(f"Items: {list(person.items())}")

# Dictionary comprehension
squares_dict = {x: x**2 for x in range(5)}
print(f"\nSquares dictionary: {squares_dict}")

# Nested data structures
print("\n=== Nested Structures ===")
nested = {
    'list_example': [1, 2, 3],
    'dict_example': {'a': 1, 'b': 2},
    'tuple_example': (4, 5, 6)
}
print(f"Nested structure: {nested}")

# Using defaultdict
from collections import defaultdict
word_count = defaultdict(int)
sentence = "the quick brown fox jumps over the lazy dog"
for word in sentence.split():
    word_count[word] += 1
print(f"\nWord count using defaultdict: {dict(word_count)}")

import os
import shutil
from pathlib import Path

# Writing to a file
with open('example.txt', 'w') as f:
    f.write('Hello, World!\n')
    f.write('This is a test file.\n')
    f.writelines(['Line 1\n', 'Line 2\n', 'Line 3\n'])

# Reading from a file
print("=== Reading file contents ===")
with open('example.txt', 'r') as f:
    content = f.read()
    print("Complete file contents:")
    print(content)

# Reading line by line
print("\nReading line by line:")
with open('example.txt', 'r') as f:
    for line in f:
        print(f"Line: {line.strip()}")

# Using pathlib
print("\n=== Using pathlib ===")
file_path = Path('example.txt')
print(f"File exists: {file_path.exists()}")
print(f"File size: {file_path.stat().st_size} bytes")
print(f"File name: {file_path.name}")
print(f"File suffix: {file_path.suffix}")

# Directory operations
print("\n=== Directory operations ===")
# Create a directory
os.makedirs('test_dir', exist_ok=True)

# List directory contents
print(f"Current directory contents: {os.listdir('.')}")

# Create some test files
for i in range(3):
    Path(f'test_dir/file{i}.txt').write_text(f'Content of file {i}')

# Using pathlib to list directory contents
test_dir = Path('test_dir')
print("\nFiles in test_dir:")
for file in test_dir.glob('*.txt'):
    print(f"- {file.name}: {file.read_text()}")

# File manipulation
source = 'example.txt'
destination = 'test_dir/example_copy.txt'
shutil.copy2(source, destination)
print(f"\nCopied {source} to {destination}")

# Clean up
print("\n=== Cleaning up ===")
os.remove('example.txt')
shutil.rmtree('test_dir')
print("Cleaned up test files and directories")

# Basic exception handling
def divide(a, b):
    try:
        result = a / b
    except ZeroDivisionError:
        print("Error: Division by zero!")
        return None
    else:
        print("Division successful!")
        return result
    finally:
        print("This code always runs")

print("Testing division function:")
print(f"10 / 2 = {divide(10, 2)}")
print(f"10 / 0 = {divide(10, 0)}")

# Multiple exception types
def process_data(data):
    try:
        number = int(data)
        result = 100 / number
    except ValueError:
        print("Error: Invalid input! Please enter a number.")
    except ZeroDivisionError:
        print("Error: Cannot divide by zero!")
    except Exception as e:
        print(f"Unexpected error: {e}")
    else:
        print(f"Result: {result}")

print("\nTesting process_data function:")
process_data("10")    # Valid number
process_data("zero")  # Invalid input
process_data("0")     # Zero division

# Custom exceptions
class AgeError(Exception):
    """Exception raised for invalid age values."""
    pass

def validate_age(age):
    try:
        age = int(age)
        if age < 0:
            raise AgeError("Age cannot be negative")
        if age > 150:
            raise AgeError("Age is unrealistically high")
    except ValueError:
        raise AgeError("Age must be a number")
    return age

print("\nTesting age validation:")
try:
    print(f"Age 25: {validate_age(25)}")
    print(f"Age -5: {validate_age(-5)}")
except AgeError as e:
    print(f"Invalid age: {e}")

# Context manager with exception handling
class FileManager:
    def __init__(self, filename):
        self.filename = filename
        self.file = None
        
    def __enter__(self):
        try:
            self.file = open(self.filename, 'w')
            return self.file
        except IOError as e:
            print(f"Error opening file: {e}")
            return None
        
    def __exit__(self, exc_type, exc_value, traceback):
        if self.file:
            self.file.close()
            print("File closed successfully")
        return False  # Don't suppress exceptions

print("\nTesting custom context manager:")
with FileManager('test.txt') as file:
    if file:
        file.write("Test content")

# Clean up
import os
if os.path.exists('test.txt'):
    os.remove('test.txt')

# Basic class definition
class Animal:
    """Base class for animals"""
    
    def __init__(self, name, species):
        self.name = name
        self.species = species
        
    def make_sound(self):
        pass
    
    def __str__(self):
        return f"{self.name} is a {self.species}"

# Inheritance
class Dog(Animal):
    def __init__(self, name, breed):
        super().__init__(name, species="Dog")
        self.breed = breed
        
    def make_sound(self):
        return "Woof!"
    
    def fetch(self, item):
        return f"{self.name} is fetching the {item}"

class Cat(Animal):
    def __init__(self, name, color):
        super().__init__(name, species="Cat")
        self.color = color
        
    def make_sound(self):
        return "Meow!"
    
    def scratch(self):
        return f"{self.name} is scratching"

# Creating and using objects
dog = Dog("Rex", "German Shepherd")
cat = Cat("Whiskers", "Orange")

print(dog)  # Using __str__
print(cat)
print(f"{dog.name} says: {dog.make_sound()}")
print(f"{cat.name} says: {cat.make_sound()}")
print(dog.fetch("ball"))
print(cat.scratch())

# Property decorators
class Circle:
    def __init__(self, radius):
        self._radius = radius
    
    @property
    def radius(self):
        return self._radius
    
    @radius.setter
    def radius(self, value):
        if value < 0:
            raise ValueError("Radius cannot be negative")
        self._radius = value
    
    @property
    def area(self):
        return 3.14159 * self._radius ** 2

# Using properties
circle = Circle(5)
print(f"\nCircle with radius {circle.radius} has area {circle.area:.2f}")
try:
    circle.radius = -1
except ValueError as e:
    print(f"Error: {e}")

# Class and static methods
class MathOperations:
    @staticmethod
    def add(x, y):
        return x + y
    
    @classmethod
    def multiply(cls, x, y):
        return x * y
    
    @staticmethod
    def is_even(n):
        return n % 2 == 0

# Using class and static methods
print(f"\nMath Operations:")
print(f"Add: {MathOperations.add(5, 3)}")
print(f"Multiply: {MathOperations.multiply(4, 2)}")
print(f"Is 6 even? {MathOperations.is_even(6)}")

# Multiple inheritance and mixins
class Flyable:
    def fly(self):
        return "Flying high!"

class Swimmable:
    def swim(self):
        return "Swimming deep!"

class Duck(Animal, Flyable, Swimmable):
    def __init__(self, name):
        super().__init__(name, species="Duck")
    
    def make_sound(self):
        return "Quack!"

# Using multiple inheritance
duck = Duck("Donald")
print(f"\nDuck capabilities:")
print(f"{duck.name} says: {duck.make_sound()}")
print(f"{duck.name}: {duck.fly()}")
print(f"{duck.name}: {duck.swim()}")

# Data classes (Python 3.7+)
from dataclasses import dataclass

@dataclass
class Point:
    x: float
    y: float
    
    def distance_from_origin(self):
        return (self.x ** 2 + self.y ** 2) ** 0.5

# Using dataclass
point = Point(3, 4)
print(f"\nPoint: ({point.x}, {point.y})")
print(f"Distance from origin: {point.distance_from_origin()}")

# Importing standard library modules
import math
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import json
from pprint import pprint

# Using imported modules
print("=== Standard Library Imports ===")
print(f"Square root of 16: {math.sqrt(16)}")
print(f"Current time: {datetime.now()}")

# Creating a Counter
words = ['apple', 'banana', 'apple', 'cherry', 'date', 'apple', 'banana']
word_counts = Counter(words)
print(f"\nWord counts: {dict(word_counts)}")

# Working with JSON
data = {
    'name': 'John',
    'age': 30,
    'skills': ['Python', 'JavaScript', 'SQL']
}

json_str = json.dumps(data, indent=2)
print("\nJSON output:")
print(json_str)

# Creating a module
import sys
from pathlib import Path

# Create a temporary module file
module_content = '''
def greet(name):
    return f"Hello, {name}!"

def factorial(n):
    if n <= 1:
        return 1
    return n * factorial(n - 1)

PI = 3.14159
'''

module_path = Path('example_module.py')
module_path.write_text(module_content)

# Import our custom module
import example_module

print("\n=== Custom Module Usage ===")
print(example_module.greet("Alice"))
print(f"Factorial of 5: {example_module.factorial(5)}")
print(f"PI from module: {example_module.PI}")

# Creating a package
package_dir = Path('example_package')
package_dir.mkdir(exist_ok=True)

# Create __init__.py
init_content = '''
from .math_utils import add, subtract
from .string_utils import reverse_string

__version__ = '1.0.0'
'''
(package_dir / '__init__.py').write_text(init_content)

# Create math_utils.py
math_utils_content = '''
def add(a, b):
    return a + b

def subtract(a, b):
    return a - b
'''
(package_dir / 'math_utils.py').write_text(math_utils_content)

# Create string_utils.py
string_utils_content = '''
def reverse_string(s):
    return s[::-1]
'''
(package_dir / 'string_utils.py').write_text(string_utils_content)

# Add package directory to Python path
sys.path.append(str(package_dir.parent))

# Import and use the package
from example_package import add, subtract, reverse_string

print("\n=== Package Usage ===")
print(f"Add: {add(5, 3)}")
print(f"Subtract: {subtract(10, 4)}")
print(f"Reverse string: {reverse_string('Hello, World!')}")

# Clean up
module_path.unlink()
for file in package_dir.glob('*.py'):
    file.unlink()
package_dir.rmdir()

# Demonstrate relative imports
print("\n=== Import System Info ===")
print(f"Python path: {sys.path}")
print(f"Module search path: {[p for p in sys.path if Path(p).exists()]}")

# Show module attributes
print("\n=== Module Attributes ===")
print(f"Module name: {__name__}")
print(f"Module file: {__file__}")
print(f"Module doc: {__doc__}")

# collections module
from collections import Counter, defaultdict, deque, namedtuple

print("=== Collections Module ===")
# Counter for counting occurrences
text = "mississippi"
char_count = Counter(text)
print(f"Character count in 'mississippi': {dict(char_count)}")

# defaultdict for automatic default values
dd = defaultdict(list)
for i in range(5):
    dd[i % 2].append(i)
print(f"\nGrouped numbers by even/odd: {dict(dd)}")

# deque for efficient queue operations
dq = deque([1, 2, 3, 4, 5], maxlen=5)
print(f"\nOriginal deque: {dq}")
dq.append(6)  # Right side
dq.appendleft(0)  # Left side
print(f"After append operations: {dq}")

# itertools module
print("\n=== Itertools Module ===")
from itertools import combinations, permutations, cycle, count, chain

# Combinations and permutations
items = ['A', 'B', 'C']
print(f"Combinations of 2: {list(combinations(items, 2))}")
print(f"Permutations of 2: {list(permutations(items, 2))}")

# Infinite iterators
counter = count(start=1, step=2)
print("\nFirst 5 odd numbers:")
for _ in range(5):
    print(next(counter), end=' ')

# functools module
print("\n\n=== Functools Module ===")
from functools import reduce, partial, lru_cache

# reduce function
numbers = [1, 2, 3, 4, 5]
product = reduce(lambda x, y: x * y, numbers)
print(f"\nProduct of numbers {numbers}: {product}")

# partial function
def power(base, exponent):
    return base ** exponent

square = partial(power, exponent=2)
cube = partial(power, exponent=3)
print(f"Square of 5: {square(5)}")
print(f"Cube of 5: {cube(5)}")

# lru_cache for memoization
@lru_cache(maxsize=None)
def fibonacci(n):
    if n < 2:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

print("\nFirst 10 Fibonacci numbers:")
for i in range(10):
    print(fibonacci(i), end=' ')

# heapq module for priority queues
print("\n\n=== Heapq Module ===")
import heapq

numbers = [10, 5, 8, 1, 9, 3]
heapq.heapify(numbers)  # Convert list to heap
print(f"\nHeap: {numbers}")
print(f"Pop 3 smallest numbers: {[heapq.heappop(numbers) for _ in range(3)]}")

# statistics module
print("\n=== Statistics Module ===")
from statistics import mean, median, mode, stdev

data = [1, 2, 2, 3, 4, 4, 4, 5]
print(f"Data: {data}")
print(f"Mean: {mean(data)}")
print(f"Median: {median(data)}")
print(f"Mode: {mode(data)}")
print(f"Standard deviation: {stdev(data)}")

# urllib for HTTP requests
print("\n=== URL Operations ===")
from urllib import request, parse

# Construct URL with parameters
params = {'q': 'python programming', 'page': 1}
url = 'https://example.com/search?' + parse.urlencode(params)
print(f"Encoded URL: {url}")

# uuid module for unique identifiers
print("\n=== UUID Module ===")
import uuid

print(f"UUID1 (MAC address + timestamp): {uuid.uuid1()}")
print(f"UUID4 (random): {uuid.uuid4()}")

# pprint for pretty printing
print("\n=== Pretty Printing ===")
from pprint import pprint

complex_data = {
    'users': [
        {'name': 'Alice', 'age': 30, 'roles': ['admin', 'user']},
        {'name': 'Bob', 'age': 25, 'roles': ['user']}
    ],
    'settings': {
        'theme': 'dark',
        'notifications': True,
        'language': 'en'
    }
}

print("Pretty printed data:")
pprint(complex_data, width=40)

# Iterators
print("=== Iterators ===")

class CountDown:
    """Custom iterator that counts down from n to 1"""
    def __init__(self, start):
        self.start = start
        
    def __iter__(self):
        return self
        
    def __next__(self):
        if self.start <= 0:
            raise StopIteration
        self.start -= 1
        return self.start + 1

# Using the iterator
countdown = CountDown(5)
print("Counting down:")
for num in countdown:
    print(num, end=' ')

# Generators
print("\n\n=== Generators ===")

def fibonacci_generator(n):
    """Generator function for Fibonacci sequence"""
    a, b = 0, 1
    for _ in range(n):
        yield a
        a, b = b, a + b

print("\nFibonacci sequence using generator:")
for num in fibonacci_generator(10):
    print(num, end=' ')

# Generator expressions
numbers = [1, 2, 3, 4, 5]
squares_gen = (x**2 for x in numbers)
print(f"\n\nSquares using generator expression: {list(squares_gen)}")

# Infinite generator
def infinite_counter(start=0):
    while True:
        yield start
        start += 1

print("\nFirst 5 numbers from infinite counter:")
counter = infinite_counter()
for _ in range(5):
    print(next(counter), end=' ')

# Decorators
print("\n\n=== Decorators ===")

# Function decorator
def timer(func):
    from time import time
    
    def wrapper(*args, **kwargs):
        start = time()
        result = func(*args, **kwargs)
        end = time()
        print(f"{func.__name__} took {end - start:.4f} seconds to execute")
        return result
    
    return wrapper

@timer
def slow_function():
    """Simulate a slow function"""
    import time
    time.sleep(1)
    return "Function completed"

print("\nTesting timer decorator:")
slow_function()

# Decorator with parameters
def repeat(times):
    def decorator(func):
        def wrapper(*args, **kwargs):
            results = []
            for _ in range(times):
                results.append(func(*args, **kwargs))
            return results
        return wrapper
    return decorator

@repeat(times=3)
def greet(name):
    return f"Hello, {name}!"

print("\nTesting repeat decorator:")
print(greet("Alice"))

# Class decorator
def singleton(cls):
    instances = {}
    
    def get_instance(*args, **kwargs):
        if cls not in instances:
            instances[cls] = cls(*args, **kwargs)
        return instances[cls]
    
    return get_instance

@singleton
class Configuration:
    def __init__(self):
        self.settings = {}
    
    def set_setting(self, key, value):
        self.settings[key] = value

print("\nTesting singleton decorator:")
config1 = Configuration()
config2 = Configuration()
print(f"Same instance? {config1 is config2}")

# Context manager using generator
from contextlib import contextmanager

@contextmanager
def temporary_file(filename):
    try:
        f = open(filename, 'w')
        yield f
    finally:
        f.close()
        import os
        os.remove(filename)

print("\nTesting context manager decorator:")
with temporary_file('test.txt') as f:
    f.write('Hello, World!')
    print("File written and automatically cleaned up")

# Property decorator with validation
class Temperature:
    def __init__(self, celsius):
        self._celsius = celsius
    
    @property
    def celsius(self):
        return self._celsius
    
    @celsius.setter
    def celsius(self, value):
        if value < -273.15:
            raise ValueError("Temperature below absolute zero!")
        self._celsius = value
    
    @property
    def fahrenheit(self):
        return (self.celsius * 9/5) + 32

print("\nTesting property decorator:")
temp = Temperature(25)
print(f"Temperature in Celsius: {temp.celsius}°C")
print(f"Temperature in Fahrenheit: {temp.fahrenheit}°F")

try:
    temp.celsius = -300
except ValueError as e:
    print(f"Error: {e}")

# Basic type hints
from typing import List, Dict, Tuple, Set, Optional, Union, Any, Callable
from dataclasses import dataclass

# Function with type hints
def greet(name: str, times: int = 1) -> str:
    """Greet someone a specified number of times."""
    return (name + "! ") * times

print(greet("Alice", 3))

# Type hints for collections
def process_numbers(numbers: List[int]) -> List[int]:
    """Double each number in the list."""
    return [n * 2 for n in numbers]

print(f"\nProcessed numbers: {process_numbers([1, 2, 3, 4, 5])}")

# Type hints for dictionaries
def count_words(text: str) -> Dict[str, int]:
    """Count word occurrences in text."""
    return {word: text.split().count(word) for word in set(text.split())}

text = "the quick brown fox jumps over the lazy dog"
print(f"\nWord count: {count_words(text)}")

# Optional and Union types
def find_index(items: List[str], target: str) -> Optional[int]:
    """Find the index of target in items, or None if not found."""
    try:
        return items.index(target)
    except ValueError:
        return None

# Union type (multiple possible types)
def process_input(value: Union[str, int]) -> str:
    if isinstance(value, str):
        return value.upper()
    return str(value * 2)

print(f"\nProcessing string: {process_input('hello')}")
print(f"Processing number: {process_input(5)}")

# Type aliases
from typing import TypeVar, Generic

T = TypeVar('T')  # Generic type variable

class Stack(Generic[T]):
    def __init__(self) -> None:
        self.items: List[T] = []
    
    def push(self, item: T) -> None:
        self.items.append(item)
    
    def pop(self) -> Optional[T]:
        return self.items.pop() if self.items else None

# Using the generic stack
number_stack: Stack[int] = Stack()
number_stack.push(1)
number_stack.push(2)
print(f"\nPopped from stack: {number_stack.pop()}")

# Type hints with dataclasses
@dataclass
class Point:
    x: float
    y: float
    label: Optional[str] = None

    def distance_from_origin(self) -> float:
        return (self.x ** 2 + self.y ** 2) ** 0.5

point = Point(3.0, 4.0, "A")
print(f"\nPoint {point.label}: ({point.x}, {point.y})")
print(f"Distance from origin: {point.distance_from_origin()}")

# Callable type hints
def apply_operation(x: int, operation: Callable[[int], int]) -> int:
    return operation(x)

def square(x: int) -> int:
    return x ** 2

print(f"\nApplying square operation: {apply_operation(5, square)}")

# Type hints with protocols (structural subtyping)
from typing import Protocol

class Drawable(Protocol):
    def draw(self) -> str: ...

class Circle:
    def draw(self) -> str:
        return "Drawing a circle"

class Square:
    def draw(self) -> str:
        return "Drawing a square"

def draw_shape(shape: Drawable) -> None:
    print(shape.draw())

print("\nDrawing shapes:")
draw_shape(Circle())
draw_shape(Square())

# Type hints with context managers
from typing import ContextManager
from contextlib import contextmanager

@contextmanager
def managed_resource(name: str) -> ContextManager[str]:
    print(f"Acquiring {name}")
    try:
        yield name
    finally:
        print(f"Releasing {name}")

print("\nUsing context manager:")
with managed_resource("test_resource") as resource:
    print(f"Using {resource}")

# Type checking with mypy
# To check types: mypy your_file.py
def demonstrates_type_error(x: int) -> str:
    # This would raise a type error when checked with mypy
    # return x + 1  # Error: Incompatible return value type
    return str(x + 1)  # Correct: converts int to str

print(f"\nDemonstrates type checking: {demonstrates_type_error(5)}")

import re

# Basic pattern matching
text = "The quick brown fox jumps over the lazy dog"
pattern = r"fox"

# Simple match
match = re.search(pattern, text)
print(f"Found '{pattern}' at position: {match.start() if match else 'not found'}")

# Find all occurrences
text_with_repeats = "The fox and the fox play in the forest"
all_matches = re.findall(r"fox", text_with_repeats)
print(f"\nFound {len(all_matches)} occurrences of 'fox'")

# Pattern with metacharacters
# \d - digit, \w - word character, \s - whitespace
phone_pattern = r"\d{3}-\d{3}-\d{4}"
phone_numbers = """
    ************
    ************
    not-a-number
"""
valid_numbers = re.findall(phone_pattern, phone_numbers)
print(f"\nValid phone numbers: {valid_numbers}")

# Character classes and quantifiers
# Match words that start with 'a' or 'A'
text = "Apple and banana are fruits. an APPLE a day keeps the doctor away"
a_words = re.findall(r'\b[Aa]\w+', text)
print(f"\nWords starting with 'a' or 'A': {a_words}")

# Groups and capturing
# Parse date in format "YYYY-MM-DD"
date_pattern = r"(\d{4})-(\d{2})-(\d{2})"
date_text = "Event date: 2025-08-09"
date_match = re.search(date_pattern, date_text)
if date_match:
    year, month, day = date_match.groups()
    print(f"\nDate parts - Year: {year}, Month: {month}, Day: {day}")

# Named groups
pattern_named = r"(?P<year>\d{4})-(?P<month>\d{2})-(?P<day>\d{2})"
date_match = re.search(pattern_named, date_text)
if date_match:
    print(f"Using named groups: {date_match.groupdict()}")

# Pattern substitution
text = "I love cats, cats are great, cats are amazing"
new_text = re.sub(r"cats", "dogs", text)
print(f"\nAfter substitution: {new_text}")

# Case-insensitive matching
text = "Python is PYTHON is Python"
matches = re.findall(r"python", text, re.IGNORECASE)
print(f"\nCase-insensitive matches: {matches}")

# Word boundaries
text = "Find word in words but not in sword"
whole_word_matches = re.findall(r"\bword\b", text)
print(f"\nWhole word matches: {whole_word_matches}")

# Email pattern matching
email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
emails = """
    <EMAIL>
    <EMAIL>
    not@<EMAIL>
    invalid.email@
"""
valid_emails = re.findall(email_pattern, emails)
print(f"\nValid email addresses: {valid_emails}")

# Greedy vs non-greedy matching
text = "<start>content1</start><start>content2</start>"
# Greedy matching
greedy = re.findall(r"<start>.*</start>", text)
# Non-greedy matching
non_greedy = re.findall(r"<start>.*?</start>", text)
print(f"\nGreedy matching: {greedy}")
print(f"Non-greedy matching: {non_greedy}")

# Compile patterns for better performance
pattern = re.compile(r"\b\w+@\w+\.\w+\b")
emails = ["<EMAIL>", "invalid.email", "<EMAIL>"]
for email in emails:
    if pattern.match(email):
        print(f"\nValid email format: {email}")
    else:
        print(f"Invalid email format: {email}")

# Lookahead and lookbehind assertions
# Positive lookahead
text = "John Doe, Jane Doe, Jim Brown"
names = re.findall(r"\w+(?=\sDoe)", text)  # Names followed by " Doe"
print(f"\nNames followed by 'Doe': {names}")

# Negative lookbehind
text = "£100, $200, €300"
amounts = re.findall(r"(?<!£)\d+", text)  # Numbers not preceded by £
print(f"Amounts not in GBP: {amounts}")

# Split string using regex
text = "apple,banana;cherry|date grape"
parts = re.split(r'[,;|\s]+', text)
print(f"\nSplit result: {parts}")

# Complex pattern example: URL parsing
url_pattern = r"""
    https?://                 # http:// or https://
    (?:www\.)?               # optional www.
    (?P<domain>[\w-]+)       # domain name
    (?P<tld>\.[a-z]{2,})    # top-level domain
    (?P<path>/[\w/.-]*)?     # optional path
"""
urls = [
    "https://www.example.com/path",
    "http://subdomain.site.co.uk/",
    "https://invalid"
]

url_regex = re.compile(url_pattern, re.VERBOSE)
for url in urls:
    match = url_regex.match(url)
    if match:
        print(f"\nURL parts for {url}:")
        print(match.groupdict())

from datetime import datetime, date, time, timedelta
from zoneinfo import ZoneInfo  # Python 3.9+
import calendar

# Current date and time
now = datetime.now()
print(f"Current datetime: {now}")
print(f"Date: {now.date()}")
print(f"Time: {now.time()}")

# Creating datetime objects
specific_date = datetime(2025, 8, 9, 15, 30, 0)
print(f"\nSpecific datetime: {specific_date}")

# Parsing datetime strings
date_string = "2025-08-09 15:30:00"
parsed_date = datetime.strptime(date_string, "%Y-%m-%d %H:%M:%S")
print(f"Parsed datetime: {parsed_date}")

# Formatting dates
formatted_date = now.strftime("%B %d, %Y at %I:%M %p")
print(f"\nFormatted datetime: {formatted_date}")

# Date arithmetic
tomorrow = now + timedelta(days=1)
next_week = now + timedelta(weeks=1)
print(f"\nTomorrow: {tomorrow.date()}")
print(f"Next week: {next_week.date()}")

# Time differences
time_diff = tomorrow - now
print(f"Time until tomorrow: {time_diff}")

# Working with time zones
utc_now = datetime.now(ZoneInfo("UTC"))
ny_now = datetime.now(ZoneInfo("America/New_York"))
tokyo_now = datetime.now(ZoneInfo("Asia/Tokyo"))

print("\nCurrent time in different zones:")
print(f"UTC: {utc_now.strftime('%Y-%m-%d %H:%M %Z')}")
print(f"New York: {ny_now.strftime('%Y-%m-%d %H:%M %Z')}")
print(f"Tokyo: {tokyo_now.strftime('%Y-%m-%d %H:%M %Z')}")

# Converting between time zones
utc_time = datetime.now(ZoneInfo("UTC"))
local_time = utc_time.astimezone()  # Convert to local time
print(f"\nUTC time: {utc_time}")
print(f"Local time: {local_time}")

# Working with calendar
print("\nCalendar operations:")
# Get calendar for current month
cal = calendar.month(now.year, now.month)
print(f"Calendar for {calendar.month_name[now.month]} {now.year}:")
print(cal)

# Check for leap year
year = 2024
is_leap = calendar.isleap(year)
print(f"\nIs {year} a leap year? {is_leap}")

# Working with time deltas
delta = timedelta(
    days=50,
    hours=8,
    minutes=15
)
future_date = now + delta
print(f"\nDate after {delta}: {future_date}")

# Date comparisons
date1 = date(2025, 1, 1)
date2 = date(2025, 12, 31)
print(f"\nComparing dates:")
print(f"{date1} is earlier than {date2}: {date1 < date2}")
print(f"Days between: {(date2 - date1).days}")

# Time objects
noon = time(12, 0, 0)
midnight = time(0, 0, 0)
print(f"\nTime comparisons:")
print(f"Noon: {noon}")
print(f"Midnight: {midnight}")
print(f"Noon is later than midnight: {noon > midnight}")

# Working with timestamps
timestamp = datetime.timestamp(now)
from_timestamp = datetime.fromtimestamp(timestamp)
print(f"\nTimestamp: {timestamp}")
print(f"Datetime from timestamp: {from_timestamp}")

# Date components
current_date = date.today()
print(f"\nDate components:")
print(f"Year: {current_date.year}")
print(f"Month: {current_date.month}")
print(f"Day: {current_date.day}")
print(f"Weekday: {current_date.strftime('%A')}")

# First and last day of month
first_day = date(current_date.year, current_date.month, 1)
_, last_day = calendar.monthrange(current_date.year, current_date.month)
last_date = date(current_date.year, current_date.month, last_day)
print(f"\nFirst day of month: {first_day}")
print(f"Last day of month: {last_date}")

# Business day calculations
def is_business_day(day):
    return day.weekday() < 5  # Monday = 0, Sunday = 6

test_date = date(2025, 8, 9)
print(f"\nIs {test_date} a business day? {is_business_day(test_date)}")

# Add business days
def add_business_days(start_date, days):
    current_date = start_date
    remaining_days = days
    while remaining_days > 0:
        current_date += timedelta(days=1)
        if is_business_day(current_date):
            remaining_days -= 1
    return current_date

start = date.today()
business_days = 5
end_date = add_business_days(start, business_days)
print(f"\nDate after {business_days} business days from {start}: {end_date}")

from contextlib import contextmanager
import time
from typing import Generator

# Basic context manager using class
class FileHandler:
    def __init__(self, filename: str, mode: str) -> None:
        self.filename = filename
        self.mode = mode
        self.file = None

    def __enter__(self):
        self.file = open(self.filename, self.mode)
        return self.file

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.file:
            self.file.close()
        # Return True to suppress exceptions, False to propagate them
        return False

# Using the class-based context manager
print("=== Class-based Context Manager ===")
with FileHandler('test.txt', 'w') as f:
    f.write('Hello from class-based context manager!')

# Read the file to verify
with FileHandler('test.txt', 'r') as f:
    content = f.read()
    print(f"File content: {content}")

# Context manager using decorator
@contextmanager
def timer() -> Generator[None, None, None]:
    """Measure execution time of a code block."""
    start = time.time()
    try:
        yield
    finally:
        end = time.time()
        print(f"Execution time: {end - start:.4f} seconds")

# Using the timer context manager
print("\n=== Timer Context Manager ===")
with timer():
    # Simulate some work
    time.sleep(1)
    print("Work completed!")

# Context manager for temporary value change
@contextmanager
def temporary_attribute(obj: object, name: str, value: any) -> Generator[None, None, None]:
    """Temporarily change an object's attribute."""
    original = getattr(obj, name)
    setattr(obj, name, value)
    try:
        yield
    finally:
        setattr(obj, name, original)

# Example class for demonstration
class Configuration:
    def __init__(self):
        self.debug = False

    def show_status(self):
        print(f"Debug mode: {self.debug}")

# Using temporary attribute context manager
print("\n=== Temporary Attribute Context Manager ===")
config = Configuration()
print("Initial status:")
config.show_status()

print("\nInside context manager:")
with temporary_attribute(config, 'debug', True):
    config.show_status()

print("\nAfter context manager:")
config.show_status()

# Nested context managers
@contextmanager
def indented_print(level: int = 1) -> Generator[None, None, None]:
    """Print with indentation."""
    prefix = "    " * level
    try:
        yield lambda x: print(f"{prefix}{x}")
    finally:
        pass

# Using nested context managers
print("\n=== Nested Context Managers ===")
with indented_print(1) as print1:
    print1("Level 1")
    with indented_print(2) as print2:
        print2("Level 2")
        with indented_print(3) as print3:
            print3("Level 3")

# Context manager for exception handling
@contextmanager
def handled_errors(*exceptions):
    """Handle specified exceptions gracefully."""
    try:
        yield
    except exceptions as e:
        print(f"Handled error: {e}")
    else:
        print("No errors occurred")

# Using error handler context manager
print("\n=== Error Handler Context Manager ===")
with handled_errors(ValueError, ZeroDivisionError):
    print("Attempting division by zero...")
    1 / 0

with handled_errors(ValueError, ZeroDivisionError):
    print("Attempting valid operation...")
    1 + 1

# Resource manager with cleanup
class DatabaseConnection:
    def __init__(self, db_name: str):
        self.db_name = db_name
        
    def connect(self):
        print(f"Connecting to database '{self.db_name}'...")
        
    def disconnect(self):
        print(f"Disconnecting from database '{self.db_name}'...")
        
    def query(self, sql: str):
        print(f"Executing query: {sql}")
        
@contextmanager
def database_connection(db_name: str) -> Generator[DatabaseConnection, None, None]:
    """Manage database connection lifecycle."""
    db = DatabaseConnection(db_name)
    try:
        db.connect()
        yield db
    finally:
        db.disconnect()

# Using database connection context manager
print("\n=== Database Connection Context Manager ===")
with database_connection("users_db") as db:
    db.query("SELECT * FROM users")

# Context manager for measuring memory usage
import os
import psutil

@contextmanager
def measure_memory() -> Generator[None, None, None]:
    """Measure memory usage of a code block."""
    process = psutil.Process(os.getpid())
    start_mem = process.memory_info().rss / 1024 / 1024  # MB
    try:
        yield
    finally:
        end_mem = process.memory_info().rss / 1024 / 1024  # MB
        print(f"Memory usage: {end_mem - start_mem:.2f} MB")

# Using memory measurement context manager
print("\n=== Memory Measurement Context Manager ===")
with measure_memory():
    # Create a large list
    big_list = list(range(1000000))
    print("Created a large list")

# Clean up
import os
if os.path.exists('test.txt'):
    os.remove('test.txt')

from contextlib import contextmanager
import time
from typing import Generator

# Basic context manager using class
class FileHandler:
    def __init__(self, filename: str, mode: str) -> None:
        self.filename = filename
        self.mode = mode
        self.file = None

    def __enter__(self):
        self.file = open(self.filename, self.mode)
        return self.file

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.file:
            self.file.close()
        # Return True to suppress exceptions, False to propagate them
        return False

# Using the class-based context manager
print("=== Class-based Context Manager ===")
with FileHandler('test.txt', 'w') as f:
    f.write('Hello from class-based context manager!')

# Read the file to verify
with FileHandler('test.txt', 'r') as f:
    content = f.read()
    print(f"File content: {content}")

# Context manager using decorator
@contextmanager
def timer() -> Generator[None, None, None]:
    """Measure execution time of a code block."""
    start = time.time()
    try:
        yield
    finally:
        end = time.time()
        print(f"Execution time: {end - start:.4f} seconds")

# Using the timer context manager
print("\n=== Timer Context Manager ===")
with timer():
    # Simulate some work
    time.sleep(1)
    print("Work completed!")

# Context manager for temporary value change
@contextmanager
def temporary_attribute(obj: object, name: str, value: any) -> Generator[None, None, None]:
    """Temporarily change an object's attribute."""
    original = getattr(obj, name)
    setattr(obj, name, value)
    try:
        yield
    finally:
        setattr(obj, name, original)

# Example class for demonstration
class Configuration:
    def __init__(self):
        self.debug = False

    def show_status(self):
        print(f"Debug mode: {self.debug}")

# Using temporary attribute context manager
print("\n=== Temporary Attribute Context Manager ===")
config = Configuration()
print("Initial status:")
config.show_status()

print("\nInside context manager:")
with temporary_attribute(config, 'debug', True):
    config.show_status()

print("\nAfter context manager:")
config.show_status()

# Nested context managers
@contextmanager
def indented_print(level: int = 1) -> Generator[None, None, None]:
    """Print with indentation."""
    prefix = "    " * level
    try:
        yield lambda x: print(f"{prefix}{x}")
    finally:
        pass

# Using nested context managers
print("\n=== Nested Context Managers ===")
with indented_print(1) as print1:
    print1("Level 1")
    with indented_print(2) as print2:
        print2("Level 2")
        with indented_print(3) as print3:
            print3("Level 3")

# Context manager for exception handling
@contextmanager
def handled_errors(*exceptions):
    """Handle specified exceptions gracefully."""
    try:
        yield
    except exceptions as e:
        print(f"Handled error: {e}")
    else:
        print("No errors occurred")

# Using error handler context manager
print("\n=== Error Handler Context Manager ===")
with handled_errors(ValueError, ZeroDivisionError):
    print("Attempting division by zero...")
    1 / 0

with handled_errors(ValueError, ZeroDivisionError):
    print("Attempting valid operation...")
    1 + 1

# Resource manager with cleanup
class DatabaseConnection:
    def __init__(self, db_name: str):
        self.db_name = db_name
        
    def connect(self):
        print(f"Connecting to database '{self.db_name}'...")
        
    def disconnect(self):
        print(f"Disconnecting from database '{self.db_name}'...")
        
    def query(self, sql: str):
        print(f"Executing query: {sql}")
        
@contextmanager
def database_connection(db_name: str) -> Generator[DatabaseConnection, None, None]:
    """Manage database connection lifecycle."""
    db = DatabaseConnection(db_name)
    try:
        db.connect()
        yield db
    finally:
        db.disconnect()

# Using database connection context manager
print("\n=== Database Connection Context Manager ===")
with database_connection("users_db") as db:
    db.query("SELECT * FROM users")

# Context manager for measuring memory usage
import os
import psutil

@contextmanager
def measure_memory() -> Generator[None, None, None]:
    """Measure memory usage of a code block."""
    process = psutil.Process(os.getpid())
    start_mem = process.memory_info().rss / 1024 / 1024  # MB
    try:
        yield
    finally:
        end_mem = process.memory_info().rss / 1024 / 1024  # MB
        print(f"Memory usage: {end_mem - start_mem:.2f} MB")

# Using memory measurement context manager
print("\n=== Memory Measurement Context Manager ===")
with measure_memory():
    # Create a large list
    big_list = list(range(1000000))
    print("Created a large list")

# Clean up
import os
if os.path.exists('test.txt'):
    os.remove('test.txt')

import os
import sys
import subprocess
from pathlib import Path

def run_command(cmd):
    """Run a command and return its output"""
    try:
        result = subprocess.run(
            cmd,
            shell=True,
            check=True,
            capture_output=True,
            text=True
        )
        return result.stdout
    except subprocess.CalledProcessError as e:
        return f"Error: {e.stderr}"

# Show current Python information
print("=== Current Python Environment ===")
print(f"Python Version: {sys.version}")
print(f"Python Executable: {sys.executable}")
print(f"Python Path: {sys.path[0]}")

# Create a virtual environment using venv
print("\n=== Creating Virtual Environment with venv ===")
venv_path = Path("example_venv")
if not venv_path.exists():
    import venv
    print("Creating new virtual environment...")
    venv.create(venv_path, with_pip=True)
    print(f"Virtual environment created at: {venv_path.absolute()}")
else:
    print(f"Virtual environment already exists at: {venv_path.absolute()}")

# Show venv structure
print("\nVirtual Environment Structure:")
venv_files = list(venv_path.glob("**/*"))
for file in sorted(venv_files):
    if file.is_file():
        print(f"- {file.relative_to(venv_path)}")

# Demonstrate activation and package installation (commands only)
print("\n=== Virtual Environment Usage Examples ===")
print("Activation Commands:")
print("Windows CMD: .\\example_venv\\Scripts\\activate.bat")
print("Windows PowerShell: .\\example_venv\\Scripts\\Activate.ps1")
print("Unix/MacOS: source example_venv/bin/activate")

# Create requirements.txt
requirements = """
# Web Framework
flask==2.0.1

# Data Processing
pandas==1.3.0
numpy==1.21.0

# Testing
pytest==6.2.5
pytest-cov==2.12.1

# Documentation
sphinx==4.0.2
"""

req_file = Path("requirements.txt")
req_file.write_text(requirements)
print("\n=== Package Management ===")
print("Created requirements.txt with contents:")
print(requirements)

# Show common pip commands
print("\nCommon pip commands:")
pip_commands = """
# Install packages
pip install package_name
pip install -r requirements.txt

# Show installed packages
pip list
pip freeze

# Upgrade packages
pip install --upgrade package_name

# Uninstall packages
pip uninstall package_name

# Show package info
pip show package_name
"""
print(pip_commands)

# Environment variables in virtual environments
print("\n=== Environment Variables ===")
env_vars = {
    "PYTHONPATH": "Add custom module directories",
    "PYTHONHOME": "Python installation directory",
    "VIRTUAL_ENV": "Current virtual environment path",
    "PATH": "System path (modified during activation)"
}

for var, description in env_vars.items():
    value = os.environ.get(var, "Not set")
    print(f"{var}: {value}")
    print(f"Purpose: {description}\n")

# Best practices
print("=== Virtual Environment Best Practices ===")
best_practices = """
1. Always use virtual environments for projects
2. Keep requirements.txt up to date
3. Use version pinning for reproducibility
4. Include only direct dependencies
5. Separate development and production dependencies
6. Use .gitignore to exclude virtual environment
7. Document environment setup in README
8. Regular dependency updates and security checks
"""
print(best_practices)

# Clean up
print("\n=== Cleaning Up ===")
if venv_path.exists():
    import shutil
    shutil.rmtree(venv_path)
    print(f"Removed virtual environment: {venv_path}")

if req_file.exists():
    req_file.unlink()
    print(f"Removed {req_file}")

# Additional tools and alternatives
print("\n=== Additional Tools ===")
tools = {
    "Poetry": "Modern dependency management and packaging",
    "Pipenv": "Combines pip and virtualenv, with lock files",
    "conda": "Package and environment management (popular in data science)",
    "pyenv": "Python version management",
    "tox": "Test automation and virtual environment management",
    "virtualenvwrapper": "Wrapper for easier virtualenv management"
}

for tool, description in tools.items():
    print(f"{tool:15} - {description}")

# Sample code to test
class Calculator:
    def add(self, a: float, b: float) -> float:
        """Add two numbers."""
        return a + b
    
    def subtract(self, a: float, b: float) -> float:
        """Subtract b from a."""
        return a - b
    
    def multiply(self, a: float, b: float) -> float:
        """Multiply two numbers."""
        return a * b
    
    def divide(self, a: float, b: float) -> float:
        """Divide a by b."""
        if b == 0:
            raise ValueError("Cannot divide by zero")
        return a / b

# Basic unit tests
def test_calculator_add():
    calc = Calculator()
    assert calc.add(2, 3) == 5, "Addition failed"
    assert calc.add(-1, 1) == 0, "Addition with negative numbers failed"
    assert calc.add(0.1, 0.2) == pytest.approx(0.3), "Float addition failed"

def test_calculator_divide():
    calc = Calculator()
    assert calc.divide(6, 2) == 3, "Division failed"
    assert calc.divide(5, 2) == 2.5, "Float division failed"
    
    # Test division by zero
    with pytest.raises(ValueError) as exc_info:
        calc.divide(1, 0)
    assert str(exc_info.value) == "Cannot divide by zero"

# Parameterized tests
@pytest.mark.parametrize("a, b, expected", [
    (2, 3, 5),
    (-1, 1, 0),
    (0, 0, 0),
    (0.1, 0.2, 0.3)
])
def test_calculator_add_parameterized(a, b, expected):
    calc = Calculator()
    assert calc.add(a, b) == pytest.approx(expected)

# Fixtures
@pytest.fixture
def calculator():
    """Provide a Calculator instance."""
    return Calculator()

@pytest.fixture
def complex_data():
    """Provide test data."""
    return {
        'numbers': [1, 2, 3, 4, 5],
        'operations': ['add', 'subtract', 'multiply', 'divide']
    }

def test_calculator_with_fixture(calculator):
    """Test calculator using fixture."""
    assert calculator.add(2, 3) == 5
    assert calculator.subtract(5, 3) == 2
    assert calculator.multiply(2, 3) == 6
    assert calculator.divide(6, 2) == 3

# Test classes
class TestCalculator:
    def setup_method(self):
        """Set up test fixtures before each test method."""
        self.calc = Calculator()
    
    def teardown_method(self):
        """Clean up after each test method."""
        pass
    
    def test_add(self):
        assert self.calc.add(2, 3) == 5
    
    def test_subtract(self):
        assert self.calc.subtract(5, 3) == 2
    
    def test_multiply(self):
        assert self.calc.multiply(2, 3) == 6
    
    def test_divide(self):
        assert self.calc.divide(6, 2) == 3

# Mock example
from unittest.mock import Mock, patch

def fetch_data(url):
    """Simulate fetching data from a URL."""
    # In real code, this would make an HTTP request
    pass

def process_data(url):
    """Process data from URL."""
    data = fetch_data(url)
    # Process the data
    return data

def test_process_data():
    """Test process_data using mock."""
    with patch('__main__.fetch_data') as mock_fetch:
        mock_fetch.return_value = {'key': 'value'}
        result = process_data('http://example.com')
        assert result == {'key': 'value'}
        mock_fetch.assert_called_once_with('http://example.com')

# Custom fixtures with cleanup
@pytest.fixture
def temp_file():
    """Create a temporary file and clean it up after test."""
    file_path = 'test_data.txt'
    with open(file_path, 'w') as f:
        f.write('Test data')
    yield file_path
    # Cleanup
    if os.path.exists(file_path):
        os.remove(file_path)

def test_with_temp_file(temp_file):
    """Test using temporary file fixture."""
    assert os.path.exists(temp_file)
    with open(temp_file) as f:
        content = f.read()
    assert content == 'Test data'

# Example of test marks
@pytest.mark.slow
def test_slow_operation():
    """Test marked as slow."""
    import time
    time.sleep(1)
    assert True

@pytest.mark.skip(reason="Not implemented yet")
def test_future_feature():
    """Test that will be implemented later."""
    pass

@pytest.mark.xfail(reason="Known bug")
def test_known_bug():
    """Test expected to fail."""
    assert False

# Doctest example
def factorial(n):
    """Calculate factorial of n.
    
    >>> factorial(0)
    1
    >>> factorial(1)
    1
    >>> factorial(5)
    120
    """
    if n < 0:
        raise ValueError("n must be non-negative")
    return 1 if n <= 1 else n * factorial(n - 1)

# Show how to run tests
if __name__ == "__main__":
    print("To run tests, use:")
    print("pytest test_file.py                     # Run all tests")
    print("pytest test_file.py -v                  # Verbose output")
    print("pytest test_file.py -k add              # Run tests matching 'add'")
    print("pytest test_file.py -m slow             # Run tests marked as 'slow'")
    print("pytest test_file.py --doctest-modules   # Run doctests")
    print("pytest test_file.py --cov=.             # Run with coverage")

import logging
import sys
from pathlib import Path
import json
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler
import traceback
import time
import threading

# Basic logging configuration
print("=== Basic Logging ===")
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# Create a logger
logger = logging.getLogger(__name__)

# Log messages at different levels
logger.debug("Debug message")
logger.info("Info message")
logger.warning("Warning message")
logger.error("Error message")
logger.critical("Critical message")

# Logging with exception information
try:
    x = 1 / 0
except Exception as e:
    logger.exception("An error occurred")

# Custom logger with multiple handlers
print("\n=== Custom Logger Configuration ===")

def setup_logger(name, log_file, level=logging.INFO):
    """Set up a logger with file and console handlers."""
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # File handler
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(level)
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    file_handler.setFormatter(file_formatter)
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(level)
    console_formatter = logging.Formatter('%(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(console_formatter)
    
    # Add handlers
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

# Create and use custom logger
app_logger = setup_logger('app_logger', 'app.log')
app_logger.info("Application started")
app_logger.warning("System resources low")

# Rotating file handler
print("\n=== Rotating File Handler ===")
rotating_logger = logging.getLogger('rotating_logger')
rotating_logger.setLevel(logging.INFO)

# Create rotating handler
rotating_handler = RotatingFileHandler(
    'rotating.log',
    maxBytes=1024,  # Rotate when file reaches 1KB
    backupCount=3   # Keep up to 3 backup files
)
rotating_handler.setFormatter(
    logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
)
rotating_logger.addHandler(rotating_handler)

# Generate some log messages
for i in range(5):
    rotating_logger.info(f"This is rotating log message {i}")

# Time-based rotating handler
print("\n=== Time-based Rotating Handler ===")
timed_logger = logging.getLogger('timed_logger')
timed_logger.setLevel(logging.INFO)

# Rotate every minute (for demonstration)
timed_handler = TimedRotatingFileHandler(
    'timed_rotating.log',
    when='M',       # Minute
    interval=1,     # Every 1 minute
    backupCount=3   # Keep up to 3 backup files
)
timed_handler.setFormatter(
    logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
)
timed_logger.addHandler(timed_handler)

timed_logger.info("This is a timed rotating log message")

# JSON logging formatter
print("\n=== JSON Logging ===")
class JsonFormatter(logging.Formatter):
    """Format log records as JSON."""
    def format(self, record):
        log_data = {
            'timestamp': self.formatTime(record),
            'name': record.name,
            'level': record.levelname,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName
        }
        if record.exc_info:
            log_data['exception'] = traceback.format_exception(*record.exc_info)
        return json.dumps(log_data)

# Create JSON logger
json_logger = logging.getLogger('json_logger')
json_logger.setLevel(logging.INFO)

json_handler = logging.FileHandler('json_logs.json')
json_handler.setFormatter(JsonFormatter())
json_logger.addHandler(json_handler)

json_logger.info("This is a JSON formatted log message")
try:
    x = 1 / 0
except Exception as e:
    json_logger.exception("Error occurred in JSON logger")

# Context manager for temporary logging level
print("\n=== Logging Context Manager ===")
class LogLevel:
    def __init__(self, logger, level):
        self.logger = logger
        self.level = level
        self.previous_level = None

    def __enter__(self):
        self.previous_level = self.logger.getEffectiveLevel()
        self.logger.setLevel(self.level)
        return self.logger

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.logger.setLevel(self.previous_level)

# Use temporary log level
debug_logger = logging.getLogger('debug_logger')
debug_logger.setLevel(logging.INFO)

debug_logger.debug("This won't show")
with LogLevel(debug_logger, logging.DEBUG):
    debug_logger.debug("This will show")
debug_logger.debug("This won't show again")

# Thread-safe logging
print("\n=== Thread-safe Logging ===")
def worker(name):
    logger = logging.getLogger(f'worker_{name}')
    for i in range(3):
        logger.info(f"Worker {name} - Message {i}")
        time.sleep(0.1)

# Create and start threads
threads = []
for i in range(3):
    thread = threading.Thread(target=worker, args=(f"Thread-{i}",))
    threads.append(thread)
    thread.start()

# Wait for all threads to complete
for thread in threads:
    thread.join()

# Cleanup log files
print("\n=== Cleaning Up Log Files ===")
log_files = ['app.log', 'rotating.log', 'timed_rotating.log', 'json_logs.json']
for log_file in log_files:
    try:
        Path(log_file).unlink()
        print(f"Removed {log_file}")
    except FileNotFoundError:
        pass

# Configuration from dictionary
print("\n=== Configuration from Dictionary ===")
config = {
    'version': 1,
    'formatters': {
        'detailed': {
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        }
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'detailed',
            'level': 'INFO'
        }
    },
    'loggers': {
        'config_logger': {
            'handlers': ['console'],
            'level': 'INFO'
        }
    }
}

import logging.config
logging.config.dictConfig(config)

config_logger = logging.getLogger('config_logger')
config_logger.info("Logger configured from dictionary")

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import requests
from PIL import Image
from io import BytesIO
import yaml
from tqdm import tqdm
import time

# NumPy examples
print("=== NumPy Examples ===")
# Create arrays
arr1 = np.array([1, 2, 3, 4, 5])
arr2 = np.array([[1, 2, 3], [4, 5, 6]])

print("Array operations:")
print(f"1D array: {arr1}")
print(f"2D array:\n{arr2}")
print(f"Mean of arr1: {arr1.mean()}")
print(f"Sum of arr2: {arr2.sum()}")
print(f"Matrix multiplication:\n{arr2 @ arr2.T}")  # Matrix multiplication

# Pandas examples
print("\n=== Pandas Examples ===")
# Create a DataFrame
data = {
    'name': ['Alice', 'Bob', 'Charlie', 'David'],
    'age': [25, 30, 35, 28],
    'city': ['New York', 'London', 'Paris', 'Tokyo']
}
df = pd.DataFrame(data)
print("\nDataFrame:")
print(df)

# Basic operations
print("\nBasic DataFrame operations:")
print("\nDescriptive statistics:")
print(df.describe())
print("\nGrouping by city:")
print(df.groupby('city').agg({'age': ['mean', 'count']}))

# Data visualization with Matplotlib
print("\n=== Matplotlib Examples ===")
plt.figure(figsize=(10, 4))

# Basic line plot
plt.subplot(1, 2, 1)
x = np.linspace(0, 10, 100)
y = np.sin(x)
plt.plot(x, y, label='sin(x)')
plt.title('Basic Line Plot')
plt.xlabel('x')
plt.ylabel('sin(x)')
plt.legend()

# Scatter plot
plt.subplot(1, 2, 2)
x = np.random.normal(0, 1, 100)
y = np.random.normal(0, 1, 100)
plt.scatter(x, y, alpha=0.5)
plt.title('Scatter Plot')
plt.xlabel('x')
plt.ylabel('y')

plt.tight_layout()
plt.show()

# Seaborn examples
print("\n=== Seaborn Examples ===")
# Create sample data
tips = sns.load_dataset('tips')

plt.figure(figsize=(10, 4))

# Box plot
plt.subplot(1, 2, 1)
sns.boxplot(x='day', y='total_bill', data=tips)
plt.title('Box Plot')

# Violin plot
plt.subplot(1, 2, 2)
sns.violinplot(x='day', y='total_bill', data=tips)
plt.title('Violin Plot')

plt.tight_layout()
plt.show()

# Requests example
print("\n=== Requests Example ===")
# Make a GET request
response = requests.get('https://api.github.com/users/python')
if response.status_code == 200:
    data = response.json()
    print("GitHub User Data:")
    print(f"Username: {data['login']}")
    print(f"Followers: {data['followers']}")
    print(f"Public repos: {data['public_repos']}")

# Pillow (PIL) example
print("\n=== Pillow Example ===")
# Create a simple image
img = Image.new('RGB', (100, 100), color='red')
img_array = np.array(img)
print(f"Image shape: {img_array.shape}")
print(f"Image mode: {img.mode}")

# Save and load image
img.save('test_image.png')
loaded_img = Image.open('test_image.png')
print(f"Loaded image size: {loaded_img.size}")

# YAML example
print("\n=== YAML Example ===")
# Create sample configuration
config = {
    'database': {
        'host': 'localhost',
        'port': 5432,
        'name': 'mydb'
    },
    'api': {
        'url': 'https://api.example.com',
        'key': 'abc123'
    }
}

# Write YAML file
with open('config.yaml', 'w') as f:
    yaml.dump(config, f, default_flow_style=False)

# Read YAML file
with open('config.yaml', 'r') as f:
    loaded_config = yaml.safe_load(f)

print("Loaded YAML configuration:")
print(yaml.dump(loaded_config, default_flow_style=False))

# tqdm example
print("\n=== tqdm Example ===")
print("Processing with progress bar:")
for i in tqdm(range(10)):
    time.sleep(0.1)  # Simulate work

# Clean up
print("\n=== Cleaning Up ===")
import os
files_to_remove = ['test_image.png', 'config.yaml']
for file in files_to_remove:
    if os.path.exists(file):
        os.remove(file)
        print(f"Removed {file}")

# Additional libraries overview
print("\n=== Other Popular Libraries ===")
libraries = {
    'Web Frameworks': ['Django', 'FastAPI', 'Flask'],
    'Data Science': ['SciPy', 'Scikit-learn', 'TensorFlow', 'PyTorch'],
    'Database': ['SQLAlchemy', 'psycopg2', 'pymongo'],
    'Testing': ['pytest', 'unittest', 'nose'],
    'Automation': ['Selenium', 'Scrapy', 'Beautiful Soup'],
    'GUI': ['tkinter', 'PyQt', 'wxPython'],
    'Networking': ['socket', 'paramiko', 'twisted'],
    'Security': ['cryptography', 'pycrypto', 'pyOpenSSL']
}

for category, libs in libraries.items():
    print(f"\n{category}:")
    for lib in libs:
        print(f"  - {lib}")